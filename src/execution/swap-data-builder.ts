import { ethers } from 'ethers';
import { Token, Pool, ArbitrageRoute, ExecutionOptions } from '../types';
import { ADDRESSES, getDexConfig } from '../config';
import { logger } from '../utils/logger';

/**
 * Swap Data Builder for generating valid transaction data for different DEXs
 * Supports Uniswap V2/V3, SushiSwap, and Curve protocols
 */
export class SwapDataBuilder {
  private provider: ethers.JsonRpcProvider;
  
  // DEX Router Interfaces
  private uniswapV2Interface: ethers.Interface;
  private uniswapV3Interface: ethers.Interface;
  private curveInterface: ethers.Interface;
  
  constructor(provider: ethers.JsonRpcProvider) {
    this.provider = provider;
    
    // Initialize DEX interfaces
    this.uniswapV2Interface = new ethers.Interface([
      'function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
      'function swapTokensForExactTokens(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
      'function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)',
      'function getAmountsIn(uint amountOut, address[] calldata path) external view returns (uint[] memory amounts)'
    ]);
    
    this.uniswapV3Interface = new ethers.Interface([
      'function exactInputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum, uint160 sqrtPriceLimitX96)) external payable returns (uint256 amountOut)',
      'function exactInput((bytes path, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum)) external payable returns (uint256 amountOut)',
      'function exactOutputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountOut, uint256 amountInMaximum, uint160 sqrtPriceLimitX96)) external payable returns (uint256 amountIn)'
    ]);
    
    this.curveInterface = new ethers.Interface([
      'function exchange(int128 i, int128 j, uint256 dx, uint256 min_dy) external returns (uint256)',
      'function get_dy(int128 i, int128 j, uint256 dx) external view returns (uint256)'
    ]);
  }

  /**
   * Build swap data for arbitrage route
   */
  async buildArbitrageSwapData(
    route: ArbitrageRoute,
    options: ExecutionOptions,
    recipient: string
  ): Promise<{ to: string; data: string; value: bigint } | null> {
    try {
      if (!route.pools || route.pools.length === 0) {
        logger.debug('No pools in arbitrage route');
        return null;
      }

      // For now, handle single-hop arbitrage (buy on one DEX, sell on another)
      const buyPool = route.pools[0];
      const sellPool = route.pools[1] || route.pools[0];
      
      if (!buyPool || !sellPool) {
        logger.debug('Invalid pool configuration in arbitrage route');
        return null;
      }

      // Get tokens from route
      const tokenIn = route.tokens[0];
      const tokenOut = route.tokens[1];
      
      if (!tokenIn || !tokenOut) {
        logger.debug('Invalid token configuration in arbitrage route');
        return null;
      }

      // Calculate swap amounts
      const swapAmount = await this.calculateOptimalSwapAmount(route, options);
      if (swapAmount <= 0n) {
        logger.debug('Invalid swap amount calculated');
        return null;
      }

      // Build swap data based on buy pool protocol
      return await this.buildSwapData(
        buyPool,
        tokenIn,
        tokenOut,
        swapAmount,
        options,
        recipient
      );

    } catch (error) {
      logger.logError(error as Error, 'SwapDataBuilder.buildArbitrageSwapData');
      return null;
    }
  }

  /**
   * Build swap data for a specific pool and token pair
   */
  async buildSwapData(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint,
    options: ExecutionOptions,
    recipient: string
  ): Promise<{ to: string; data: string; value: bigint } | null> {
    try {
      const deadline = Math.floor(Date.now() / 1000) + 300; // 5 minutes
      const minAmountOut = await this.calculateMinAmountOut(
        pool,
        tokenIn,
        tokenOut,
        amountIn,
        options.slippageTolerance || 0.5
      );

      switch (pool.protocol) {
        case 'uniswap-v2':
          return this.buildUniswapV2SwapData(pool, tokenIn, tokenOut, amountIn, minAmountOut, recipient, deadline);
        
        case 'uniswap-v3':
          return this.buildUniswapV3SwapData(pool, tokenIn, tokenOut, amountIn, minAmountOut, recipient, deadline);
        
        case 'curve':
          return this.buildCurveSwapData(pool, tokenIn, tokenOut, amountIn, minAmountOut, recipient);
        
        default:
          logger.debug(`Unsupported protocol: ${pool.protocol}`);
          return null;
      }
    } catch (error) {
      logger.logError(error as Error, 'SwapDataBuilder.buildSwapData');
      return null;
    }
  }

  /**
   * Build Uniswap V2 swap data
   */
  private async buildUniswapV2SwapData(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint,
    minAmountOut: bigint,
    recipient: string,
    deadline: number
  ): Promise<{ to: string; data: string; value: bigint }> {
    const path = [tokenIn.address, tokenOut.address];
    
    const data = this.uniswapV2Interface.encodeFunctionData('swapExactTokensForTokens', [
      amountIn,
      minAmountOut,
      path,
      recipient,
      deadline
    ]);

    return {
      to: this.getRouterAddress(pool.protocol),
      data,
      value: 0n
    };
  }

  /**
   * Build Uniswap V3 swap data
   */
  private async buildUniswapV3SwapData(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint,
    minAmountOut: bigint,
    recipient: string,
    deadline: number
  ): Promise<{ to: string; data: string; value: bigint }> {
    const params = {
      tokenIn: tokenIn.address,
      tokenOut: tokenOut.address,
      fee: pool.fee || 3000,
      recipient,
      deadline,
      amountIn,
      amountOutMinimum: minAmountOut,
      sqrtPriceLimitX96: 0
    };

    const data = this.uniswapV3Interface.encodeFunctionData('exactInputSingle', [params]);

    return {
      to: this.getRouterAddress(pool.protocol),
      data,
      value: 0n
    };
  }

  /**
   * Build Curve swap data
   */
  private async buildCurveSwapData(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint,
    minAmountOut: bigint,
    recipient: string
  ): Promise<{ to: string; data: string; value: bigint }> {
    if (!pool.curveTokenIndices) {
      throw new Error('Curve pool missing token indices');
    }

    const { i, j } = pool.curveTokenIndices;
    
    const data = this.curveInterface.encodeFunctionData('exchange', [
      i,
      j,
      amountIn,
      minAmountOut
    ]);

    return {
      to: pool.address, // Curve pools are called directly
      data,
      value: 0n
    };
  }

  /**
   * Calculate optimal swap amount for arbitrage
   */
  private async calculateOptimalSwapAmount(
    route: ArbitrageRoute,
    options: ExecutionOptions
  ): Promise<bigint> {
    try {
      // For now, use a conservative amount based on expected profit
      // In production, this would use more sophisticated calculations
      const baseAmount = ethers.parseEther('0.1'); // 0.1 ETH equivalent
      const maxAmount = ethers.parseEther('1.0');   // 1.0 ETH equivalent
      
      // Scale based on expected profit
      const profitRatio = Number(route.expectedProfit) / Number(ethers.parseEther('0.01'));
      const scaledAmount = baseAmount * BigInt(Math.max(1, Math.min(10, profitRatio)));
      
      return scaledAmount > maxAmount ? maxAmount : scaledAmount;
    } catch (error) {
      logger.debug('Error calculating optimal swap amount:', error);
      return ethers.parseEther('0.1'); // Default fallback
    }
  }

  /**
   * Calculate minimum amount out with slippage tolerance
   */
  private async calculateMinAmountOut(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint,
    slippageTolerance: number
  ): Promise<bigint> {
    try {
      // Simplified calculation - in production, use actual pool pricing
      const slippageMultiplier = Math.floor((1 - slippageTolerance / 100) * 10000);
      const estimatedOut = amountIn; // 1:1 ratio as fallback
      
      return (estimatedOut * BigInt(slippageMultiplier)) / BigInt(10000);
    } catch (error) {
      logger.debug('Error calculating min amount out:', error);
      return 0n; // Conservative fallback
    }
  }

  /**
   * Get router address for protocol
   */
  private getRouterAddress(protocol: string): string {
    const dexConfig = getDexConfig();
    
    switch (protocol) {
      case 'uniswap-v2':
        return dexConfig.UNISWAP_V2.router;
      case 'uniswap-v3':
        return dexConfig.UNISWAP_V3.router;
      default:
        return dexConfig.UNISWAP_V2.router; // Default fallback
    }
  }

  /**
   * Validate swap data before execution
   */
  validateSwapData(swapData: { to: string; data: string; value: bigint }): boolean {
    return (
      swapData.to &&
      ethers.isAddress(swapData.to) &&
      swapData.data &&
      swapData.data !== '0x' &&
      swapData.data.length > 10 // Basic data length check
    );
  }
}
