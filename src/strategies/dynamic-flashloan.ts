import { ethers } from 'ethers';
import { FlashloanRoute, ArbitrageRoute, Pool, Token } from '../types';
import { FlashloanStrategy } from './flashloan';
import { BalancerFlashloanStrategy } from './balancer-flashloan';
import { UniswapV3FlashSwapStrategy } from './uniswap-v3-flash';
import { MEVShareFlashloanStrategy } from './mev-share-flashloan';
import { FlashbotsBundleManager } from '../flashbots/bundle-provider';
import { FlashbotsExecutor } from '../execution/flashbots-executor';
import { config } from '../config';
import { logger } from '../utils/logger';

/**
 * Strategy performance statistics
 */
interface StrategyStats {
  totalOpportunities: number;
  successfulExecutions: number;
  totalProfit: bigint;
  averageGasCost: bigint;
  averageExecutionTime: number;
  successRate: number;
  profitability: number;
}

/**
 * Enhanced flashloan route with strategy information
 */
interface EnhancedFlashloanRoute extends FlashloanRoute {
  strategy: 'aave' | 'balancer' | 'uniswap-v3' | 'mev-share';
  estimatedGasCost: bigint;
  netProfit: bigint;
  riskScore: number;
  executionComplexity: number;
}

/**
 * Dynamic Flashloan Strategy Selector
 * Combines all flashloan strategies and selects the most profitable one
 */
export class DynamicFlashloanStrategy {
  private provider: ethers.Provider;
  private wallet: ethers.Wallet;
  private flashbotsManager: FlashbotsBundleManager;
  private flashbotsExecutor: FlashbotsExecutor;

  // Individual strategies
  private aaveFlashloanStrategy: FlashloanStrategy;
  private balancerFlashloanStrategy: BalancerFlashloanStrategy;
  private uniswapV3FlashSwapStrategy: UniswapV3FlashSwapStrategy;
  private mevShareFlashloanStrategy: MEVShareFlashloanStrategy | null = null;

  // Strategy performance tracking
  private strategyStats: Map<string, StrategyStats> = new Map();

  constructor(
    provider: ethers.Provider,
    wallet: ethers.Wallet,
    flashbotsManager: FlashbotsBundleManager,
    flashbotsExecutor: FlashbotsExecutor
  ) {
    this.provider = provider;
    this.wallet = wallet;
    this.flashbotsManager = flashbotsManager;
    this.flashbotsExecutor = flashbotsExecutor;

    // Initialize individual strategies
    this.aaveFlashloanStrategy = new FlashloanStrategy(provider);
    this.balancerFlashloanStrategy = new BalancerFlashloanStrategy(provider);
    this.uniswapV3FlashSwapStrategy = new UniswapV3FlashSwapStrategy(provider as ethers.JsonRpcProvider, wallet);

    // Initialize strategy statistics
    this.initializeStrategyStats();

    logger.info('🚀 Dynamic Flashloan Strategy initialized');
    logger.info('   Strategies: Aave, Balancer, Uniswap V3, MEV-Share');
  }

  /**
   * Initialize strategy performance tracking
   */
  private initializeStrategyStats(): void {
    const strategies = ['aave', 'balancer', 'uniswap-v3', 'mev-share'];
    
    for (const strategy of strategies) {
      this.strategyStats.set(strategy, {
        totalOpportunities: 0,
        successfulExecutions: 0,
        totalProfit: BigInt(0),
        averageGasCost: BigInt(0),
        averageExecutionTime: 0,
        successRate: 0,
        profitability: 0
      });
    }
  }

  /**
   * Set MEV-Share strategy (called by bot after initialization)
   */
  setMEVShareStrategy(strategy: MEVShareFlashloanStrategy): void {
    this.mevShareFlashloanStrategy = strategy;
    logger.info('✅ MEV-Share strategy integrated');
  }

  /**
   * Scan for all flashloan opportunities across all strategies
   */
  async scanForOpportunities(): Promise<EnhancedFlashloanRoute[]> {
    const startTime = Date.now();
    const allOpportunities: EnhancedFlashloanRoute[] = [];

    try {
      // Validate configuration before scanning
      if (!this.validateConfiguration()) {
        logger.debug('Dynamic flashloan strategy configuration invalid, skipping opportunity search');
        return [];
      }

      logger.info('🔍 Scanning for dynamic flashloan opportunities...');

      // Scan all strategies in parallel for maximum efficiency
      const [
        aaveOpportunities,
        balancerOpportunities,
        uniswapV3Opportunities
      ] = await Promise.all([
        this.scanAaveOpportunities(),
        this.scanBalancerOpportunities(),
        this.scanUniswapV3Opportunities()
      ]);

      // Combine all opportunities
      allOpportunities.push(...aaveOpportunities);
      allOpportunities.push(...balancerOpportunities);
      allOpportunities.push(...uniswapV3Opportunities);

      // Rank opportunities by profitability and confidence
      const rankedOpportunities = this.rankOpportunities(allOpportunities);

      const scanTime = Date.now() - startTime;
      logger.info(`✅ Dynamic scan completed in ${scanTime}ms`);
      logger.info(`   Found ${rankedOpportunities.length} opportunities`);

      if (rankedOpportunities.length > 0) {
        const best = rankedOpportunities[0];
        logger.info(`   Best: ${best.strategy} - ${ethers.formatEther(best.expectedProfit)} ETH (${best.confidence}%)`);
      }

      return rankedOpportunities;

    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.scanForOpportunities');
      logger.error('Dynamic flashloan scan failed', error);
      return [];
    }
  }

  /**
   * Validate configuration before executing strategies
   */
  private validateConfiguration(): boolean {
    // Check if required contracts are configured
    const hasValidHybridContract = config.hybridFlashloanContract &&
                                  ethers.isAddress(config.hybridFlashloanContract);

    const hasValidAaveContract = config.aaveFlashloanContract &&
                                ethers.isAddress(config.aaveFlashloanContract);

    const hasValidBalancerContract = config.balancerFlashloanContract &&
                                    ethers.isAddress(config.balancerFlashloanContract);

    // At least one contract should be valid
    if (!hasValidHybridContract && !hasValidAaveContract && !hasValidBalancerContract) {
      logger.debug('No valid flashloan contracts configured');
      return false;
    }

    return true;
  }

  /**
   * Scan Aave flashloan opportunities
   */
  private async scanAaveOpportunities(): Promise<EnhancedFlashloanRoute[]> {
    try {
      const opportunities = await this.aaveFlashloanStrategy.scanForFlashloanOpportunities();
      return opportunities.map(route => this.enhanceRoute(route, 'aave'));
    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.scanAaveOpportunities');
      return [];
    }
  }

  /**
   * Scan Balancer flashloan opportunities
   */
  private async scanBalancerOpportunities(): Promise<EnhancedFlashloanRoute[]> {
    try {
      const opportunities = await this.balancerFlashloanStrategy.scanForBalancerFlashloanOpportunities();
      return opportunities.map(route => this.enhanceRoute(route, 'balancer'));
    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.scanBalancerOpportunities');
      return [];
    }
  }

  /**
   * Scan Uniswap V3 flash swap opportunities
   */
  private async scanUniswapV3Opportunities(): Promise<EnhancedFlashloanRoute[]> {
    try {
      const opportunities = await this.uniswapV3FlashSwapStrategy.scanForOpportunities();
      return opportunities.map(route => this.enhanceV3Route(route));
    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.scanUniswapV3Opportunities');
      return [];
    }
  }

  /**
   * Enhance V3 route with strategy-specific information
   */
  private enhanceV3Route(route: any): EnhancedFlashloanRoute {
    // Convert V3 route to enhanced flashloan route format
    const enhancedRoute: EnhancedFlashloanRoute = {
      flashloanToken: { address: route.tokenA, symbol: 'TOKEN', name: 'Token A', decimals: 18 },
      flashloanAmount: route.amount,
      flashloanPremium: BigInt(0),
      arbitrageRoute: {
        tokens: [
          { address: route.tokenA, symbol: 'TOKEN_A', name: 'Token A', decimals: 18 },
          { address: route.tokenB, symbol: 'TOKEN_B', name: 'Token B', decimals: 18 }
        ],
        pools: [],
        expectedProfit: route.expectedProfit || BigInt(0),
        gasEstimate: BigInt(300000),
        confidence: route.confidence || 50
      },
      expectedProfit: route.expectedProfit || BigInt(0),
      confidence: route.confidence || 50,
      gasEstimate: BigInt(300000),
      strategy: 'uniswap-v3',
      estimatedGasCost: this.estimateGasCost('uniswap-v3'),
      netProfit: BigInt(route.expectedProfit?.toString() || '0') - this.estimateGasCost('uniswap-v3'),
      riskScore: this.calculateRiskScore(route, 'uniswap-v3'),
      executionComplexity: this.calculateExecutionComplexity('uniswap-v3')
    };

    return enhancedRoute;
  }

  /**
   * Enhance route with strategy-specific information
   */
  private enhanceRoute(route: FlashloanRoute, strategy: 'aave' | 'balancer' | 'uniswap-v3' | 'mev-share'): EnhancedFlashloanRoute {
    // Estimate gas costs based on strategy
    const estimatedGasCost = this.estimateGasCost(strategy);
    
    // Calculate net profit after gas costs
    const netProfit = BigInt(route.expectedProfit.toString()) - estimatedGasCost;
    
    // Calculate risk score (lower is better)
    const riskScore = this.calculateRiskScore(route, strategy);
    
    // Calculate execution complexity (lower is better)
    const executionComplexity = this.calculateExecutionComplexity(strategy);

    return {
      ...route,
      strategy,
      estimatedGasCost,
      netProfit,
      riskScore,
      executionComplexity
    };
  }

  /**
   * Estimate gas cost for different strategies
   */
  private estimateGasCost(strategy: string): bigint {
    const baseGasPrice = BigInt(20e9); // 20 gwei
    
    switch (strategy) {
      case 'aave':
        return BigInt(400000) * baseGasPrice; // Aave has 0.09% fees but higher gas
      case 'balancer':
        return BigInt(350000) * baseGasPrice; // Balancer has 0% fees, moderate gas
      case 'uniswap-v3':
        return BigInt(300000) * baseGasPrice; // Uniswap V3 flash swaps, lower gas
      case 'mev-share':
        return BigInt(450000) * baseGasPrice; // MEV-Share has additional complexity
      default:
        return BigInt(400000) * baseGasPrice;
    }
  }

  /**
   * Calculate risk score for a route and strategy
   */
  private calculateRiskScore(route: FlashloanRoute, strategy: string): number {
    let riskScore = 0;

    // Base risk from confidence (lower confidence = higher risk)
    riskScore += (100 - route.confidence) * 0.5;

    // Strategy-specific risks
    switch (strategy) {
      case 'aave':
        riskScore += 10; // Aave fees add risk
        break;
      case 'balancer':
        riskScore += 5; // Balancer is generally safer
        break;
      case 'uniswap-v3':
        riskScore += 15; // V3 flash swaps have more complexity
        break;
      case 'mev-share':
        riskScore += 20; // MEV-Share has timing risks
        break;
    }

    // Profit size risk (very large profits might be suspicious)
    const profitEth = Number(ethers.formatEther(route.expectedProfit));
    if (profitEth > 10) riskScore += 25;
    else if (profitEth > 5) riskScore += 15;
    else if (profitEth > 1) riskScore += 5;

    return Math.min(riskScore, 100); // Cap at 100
  }

  /**
   * Calculate execution complexity
   */
  private calculateExecutionComplexity(strategy: string): number {
    switch (strategy) {
      case 'balancer':
        return 1; // Simplest - direct flashloan
      case 'aave':
        return 2; // Moderate - flashloan with fees
      case 'uniswap-v3':
        return 3; // Complex - flash swaps
      case 'mev-share':
        return 4; // Most complex - bundle coordination
      default:
        return 2;
    }
  }

  /**
   * Rank opportunities by profitability, confidence, and risk
   */
  private rankOpportunities(opportunities: EnhancedFlashloanRoute[]): EnhancedFlashloanRoute[] {
    return opportunities
      .filter(route => route.netProfit > BigInt(0)) // Only profitable after gas
      .sort((a, b) => {
        // Primary: Net profit (higher is better)
        const profitDiff = Number(b.netProfit - a.netProfit);
        if (Math.abs(profitDiff) > 1e15) return profitDiff > 0 ? 1 : -1; // 0.001 ETH threshold

        // Secondary: Confidence (higher is better)
        const confidenceDiff = b.confidence - a.confidence;
        if (Math.abs(confidenceDiff) > 5) return confidenceDiff;

        // Tertiary: Risk score (lower is better)
        const riskDiff = a.riskScore - b.riskScore;
        if (Math.abs(riskDiff) > 5) return riskDiff;

        // Quaternary: Execution complexity (lower is better)
        return a.executionComplexity - b.executionComplexity;
      });
  }

  /**
   * Execute the best flashloan opportunity with MEV protection
   */
  async executeBestOpportunity(): Promise<boolean> {
    try {
      const opportunities = await this.scanForOpportunities();

      if (opportunities.length === 0) {
        logger.info('❌ No profitable flashloan opportunities found');
        return false;
      }

      const bestOpportunity = opportunities[0];
      logger.info(`🎯 Executing best opportunity: ${bestOpportunity.strategy}`);
      logger.info(`   Expected profit: ${ethers.formatEther(bestOpportunity.netProfit)} ETH`);
      logger.info(`   Confidence: ${bestOpportunity.confidence}%`);
      logger.info(`   Risk score: ${bestOpportunity.riskScore}/100`);

      // Execute with MEV protection via Flashbots
      const success = await this.executeWithMEVProtection(bestOpportunity);

      // Update strategy statistics
      this.updateStrategyStats(bestOpportunity.strategy, success, bestOpportunity.netProfit);

      return success;

    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.executeBestOpportunity');
      logger.error('Failed to execute best opportunity', error);
      return false;
    }
  }

  /**
   * Execute flashloan with MEV protection via Flashbots
   */
  private async executeWithMEVProtection(route: EnhancedFlashloanRoute): Promise<boolean> {
    try {
      logger.info('🛡️  Executing with MEV protection via Flashbots...');

      // Check if Flashbots is available
      if (!this.flashbotsManager.isAvailable()) {
        logger.info('⚠️  Flashbots not available, using regular execution');
        return await this.executeRegular(route);
      }

      // Execute based on strategy
      switch (route.strategy) {
        case 'aave':
          return await this.executeAaveWithFlashbots(route);
        case 'balancer':
          return await this.executeBalancerWithFlashbots(route);
        case 'uniswap-v3':
          return await this.executeUniswapV3WithFlashbots(route);
        case 'mev-share':
          return await this.executeMEVShareWithFlashbots(route);
        default:
          logger.info(`❌ Unknown strategy: ${route.strategy}`);
          return false;
      }

    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.executeWithMEVProtection');
      logger.error('MEV-protected execution failed', error);
      return false;
    }
  }

  /**
   * Execute Aave flashloan with Flashbots
   */
  private async executeAaveWithFlashbots(route: EnhancedFlashloanRoute): Promise<boolean> {
    try {
      logger.info('🏦 Executing Aave flashloan via Flashbots...');

      const result = await this.flashbotsExecutor.executeFlashloan(route, {
        useFlashbots: true,
        urgency: 'fast',
        maxGasCostEth: 0.02,
        slippageTolerance: 0.5
      });

      if (result.success) {
        logger.info('✅ Aave flashloan executed successfully');
        logger.info(`   Transaction: ${result.txHash}`);
        return true;
      } else {
        logger.info(`❌ Aave flashloan failed: ${result.error}`);
        return false;
      }

    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.executeAaveWithFlashbots');
      return false;
    }
  }

  /**
   * Execute Balancer flashloan with Flashbots
   */
  private async executeBalancerWithFlashbots(route: EnhancedFlashloanRoute): Promise<boolean> {
    try {
      logger.info('⚖️  Executing Balancer flashloan via Flashbots...');

      const success = await this.balancerFlashloanStrategy.executeBalancerFlashloan(route);

      if (success) {
        logger.info('✅ Balancer flashloan executed successfully');
        return true;
      } else {
        logger.info('❌ Balancer flashloan execution failed');
        return false;
      }

    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.executeBalancerWithFlashbots');
      return false;
    }
  }

  /**
   * Execute Uniswap V3 flash swap with Flashbots
   */
  private async executeUniswapV3WithFlashbots(route: EnhancedFlashloanRoute): Promise<boolean> {
    try {
      logger.info('🦄 Executing Uniswap V3 flash swap via Flashbots...');

      // Convert route to V3 flash swap format
      const v3Route = this.convertToV3Route(route);
      const result = await this.uniswapV3FlashSwapStrategy.executeFlashSwap(v3Route);

      if (result.success) {
        logger.info('✅ Uniswap V3 flash swap executed successfully');
        return true;
      } else {
        logger.info('❌ Uniswap V3 flash swap execution failed');
        return false;
      }

    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.executeUniswapV3WithFlashbots');
      return false;
    }
  }

  /**
   * Execute MEV-Share flashloan with Flashbots
   */
  private async executeMEVShareWithFlashbots(route: EnhancedFlashloanRoute): Promise<boolean> {
    try {
      logger.info('🤝 Executing MEV-Share flashloan via Flashbots...');

      if (!this.mevShareFlashloanStrategy) {
        logger.info('❌ MEV-Share strategy not available');
        return false;
      }

      // MEV-Share strategy handles its own Flashbots execution
      const success = await this.mevShareFlashloanStrategy.executeBalancerFlashloan(route);

      if (success) {
        logger.info('✅ MEV-Share flashloan executed successfully');
        return true;
      } else {
        logger.info('❌ MEV-Share flashloan execution failed');
        return false;
      }

    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.executeMEVShareWithFlashbots');
      return false;
    }
  }

  /**
   * Execute without MEV protection (fallback)
   */
  private async executeRegular(route: EnhancedFlashloanRoute): Promise<boolean> {
    try {
      switch (route.strategy) {
        case 'aave':
          return await this.aaveFlashloanStrategy.executeFlashloan(route);
        case 'balancer':
          return await this.balancerFlashloanStrategy.executeBalancerFlashloan(route);
        case 'uniswap-v3':
          const v3Route = this.convertToV3Route(route);
          const result = await this.uniswapV3FlashSwapStrategy.executeFlashSwap(v3Route);
          return result.success;
        default:
          return false;
      }
    } catch (error) {
      logger.logError(error as Error, 'DynamicFlashloanStrategy.executeRegular');
      return false;
    }
  }

  /**
   * Convert flashloan route to Uniswap V3 format
   */
  private convertToV3Route(route: FlashloanRoute): any {
    // Convert the generic flashloan route to V3-specific format
    return {
      tokenA: route.flashloanToken.address,
      tokenB: route.arbitrageRoute.tokens[1].address,
      borrowFee: 3000, // Default to 0.3% fee tier
      sellFee: 500,    // Default to 0.05% fee tier
      amount: route.flashloanAmount,
      expectedProfit: route.expectedProfit,
      confidence: route.confidence
    };
  }

  /**
   * Update strategy performance statistics
   */
  private updateStrategyStats(strategy: string, success: boolean, profit: bigint): void {
    const stats = this.strategyStats.get(strategy);
    if (!stats) return;

    stats.totalOpportunities++;
    if (success) {
      stats.successfulExecutions++;
      stats.totalProfit += profit;
    }

    stats.successRate = (stats.successfulExecutions / stats.totalOpportunities) * 100;
    stats.profitability = Number(ethers.formatEther(stats.totalProfit));

    this.strategyStats.set(strategy, stats);

    logger.info(`📊 ${strategy} stats: ${stats.successRate.toFixed(1)}% success, ${stats.profitability.toFixed(4)} ETH total`);
  }

  /**
   * Get strategy performance report
   */
  getStrategyReport(): Record<string, StrategyStats> {
    const report: Record<string, StrategyStats> = {};

    for (const [strategy, stats] of this.strategyStats.entries()) {
      report[strategy] = { ...stats };
    }

    return report;
  }

  /**
   * Get the best performing strategy
   */
  getBestStrategy(): string {
    let bestStrategy = 'balancer'; // Default to Balancer (0% fees)
    let bestScore = 0;

    for (const [strategy, stats] of this.strategyStats.entries()) {
      // Calculate composite score: success rate * profitability
      const score = stats.successRate * stats.profitability;

      if (score > bestScore) {
        bestScore = score;
        bestStrategy = strategy;
      }
    }

    return bestStrategy;
  }

  /**
   * Check if any strategy is currently profitable
   */
  async isAnyStrategyProfitable(): Promise<boolean> {
    const opportunities = await this.scanForOpportunities();
    return opportunities.length > 0 && opportunities[0].netProfit > BigInt(0);
  }

  /**
   * Get current market conditions summary
   */
  async getMarketConditions(): Promise<{
    totalOpportunities: number;
    bestProfit: string;
    bestStrategy: string;
    averageConfidence: number;
    flashbotsAvailable: boolean;
  }> {
    const opportunities = await this.scanForOpportunities();

    const totalOpportunities = opportunities.length;
    const bestProfit = opportunities.length > 0 ? ethers.formatEther(opportunities[0].netProfit) : '0';
    const bestStrategy = opportunities.length > 0 ? opportunities[0].strategy : 'none';
    const averageConfidence = opportunities.length > 0
      ? opportunities.reduce((sum, op) => sum + op.confidence, 0) / opportunities.length
      : 0;
    const flashbotsAvailable = this.flashbotsManager.isAvailable();

    return {
      totalOpportunities,
      bestProfit,
      bestStrategy,
      averageConfidence,
      flashbotsAvailable
    };
  }
}
